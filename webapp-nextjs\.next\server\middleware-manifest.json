{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "a9298a114db3efa3205d218e92d0760c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b582afb38461dea607d2c422d999079d3d557d5b2804eccf372628b2c99ba9db", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9ea16fe35bbf2ae73c31df40108dadd162d9c1b6dc72d5bd67e5bd57acd0238b"}}}, "sortedMiddleware": ["/"], "functions": {}}