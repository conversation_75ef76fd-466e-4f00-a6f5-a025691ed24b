'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Cable, Loader2, Search } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { Cavo, ParcoCavo } from '@/types'

interface CavoConMetri extends Cavo {
  _isIncompatible?: boolean
}

interface AggiungiCaviDialogTestProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  cantiereId: number
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function AggiungiCaviDialogTest({
  open,
  onClose,
  bobina,
  cantiereId,
  onSuccess,
  onError
}: AggiungiCaviDialogTestProps) {
  // Stati base
  const [caviCompatibili, setCaviCompatibili] = useState<CavoConMetri[]>([])
  const [caviIncompatibili, setCaviIncompatibili] = useState<CavoConMetri[]>([])
  const [caviSelezionati, setCaviSelezionati] = useState<CavoConMetri[]>([])
  const [caviMetri, setCaviMetri] = useState<Record<string, string>>({})
  const [caviLoading, setCaviLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Carica i cavi disponibili
  const loadCavi = async () => {
    if (!cantiereId || !bobina) {
      console.log('❌ Mancano dati:', { cantiereId, bobina: !!bobina })
      return
    }

    try {
      setCaviLoading(true)
      console.log('🔍 TEST - Caricamento cavi per cantiere:', cantiereId)
      
      const caviData = await caviApi.getCavi(cantiereId)
      console.log('📦 TEST - Cavi ricevuti:', caviData.length)
      
      if (caviData.length > 0) {
        console.log('📋 TEST - Primo cavo esempio:', caviData[0])
      }

      // Filtro ESATTO come webapp originale
      const caviDisponibili = caviData.filter(cavo => {
        const metriReali = parseFloat(cavo.metratura_reale?.toString() || '0') || 0
        const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0') || 0
        
        const isNotInstalled = !(cavo.stato_installazione === 'Installato' || metriReali > 0)
        const isNotSpare = cavo.modificato_manualmente !== 3
        const hasTheoreticalMeters = metriTeorici > 0
        
        return isNotInstalled && isNotSpare && metriReali === 0 && hasTheoreticalMeters
      })

      console.log('📊 TEST - Cavi disponibili dopo filtro:', caviDisponibili.length)

      // Compatibilità
      const compatibili = caviDisponibili.filter(cavo => 
        cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione)
      )
      
      const incompatibili = caviDisponibili.filter(cavo => 
        !(cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione))
      )

      console.log('✅ TEST - RISULTATO FINALE:')
      console.log('- Compatibili:', compatibili.length)
      console.log('- Incompatibili:', incompatibili.length)

      setCaviCompatibili(compatibili)
      setCaviIncompatibili(incompatibili)
    } catch (error: any) {
      console.error('❌ TEST - Errore caricamento cavi:', error)
      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'))
    } finally {
      setCaviLoading(false)
    }
  }

  // Reset quando si apre il dialog
  useEffect(() => {
    console.log('🔄 TEST - Dialog opened:', { 
      open, 
      bobina: !!bobina, 
      cantiereId
    })
    
    if (open && bobina && cantiereId > 0) {
      console.log('✅ TEST - Caricamento cavi...')
      setCaviSelezionati([])
      setCaviMetri({})
      loadCavi()
    }
  }, [open, bobina, cantiereId])

  // Gestisce la selezione di un cavo
  const handleCavoToggle = (cavo: CavoConMetri, isCompatible: boolean) => {
    console.log('🔘 TEST - Cavo toggle clicked:', { 
      cavoId: cavo.id_cavo, 
      isCompatible
    })
    
    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)

    if (isSelected) {
      // Rimuovi dalla selezione
      console.log('➖ TEST - Rimuovendo cavo dalla selezione')
      setCaviSelezionati(prev => prev.filter(c => c.id_cavo !== cavo.id_cavo))
      setCaviMetri(prev => {
        const newMetri = { ...prev }
        delete newMetri[cavo.id_cavo]
        return newMetri
      })
    } else {
      // Aggiungi alla selezione
      console.log('➕ TEST - Aggiungendo cavo alla selezione')
      setCaviSelezionati(prev => [...prev, cavo])
      setCaviMetri(prev => ({
        ...prev,
        [cavo.id_cavo]: cavo.metri_teorici?.toString() || '0'
      }))
    }
  }

  // Gestisce il cambio dei metri
  const handleMetriChange = (cavoId: string, value: string) => {
    setCaviMetri(prev => ({
      ...prev,
      [cavoId]: value
    }))
  }

  // Gestisce il salvataggio
  const handleSave = async () => {
    if (!cantiereId || !bobina) return

    try {
      setSaving(true)
      console.log('💾 TEST - Salvando cavi:', caviSelezionati.length)

      for (const cavo of caviSelezionati) {
        const metriPosati = parseFloat(caviMetri[cavo.id_cavo])
        
        await caviApi.updateMetriPosati(
          cantiereId,
          cavo.id_cavo,
          metriPosati,
          bobina.id_bobina
        )
      }

      onSuccess(`${caviSelezionati.length} cavi aggiornati con successo`)
      onClose()
    } catch (error: any) {
      console.error('❌ TEST - Errore nel salvataggio:', error)
      onError('Errore durante il salvataggio dei cavi')
    } finally {
      setSaving(false)
    }
  }

  if (!bobina) return null

  const getBobinaNumber = (idBobina: string) => {
    const match = idBobina.match(/C\d+_B(\d+)/)
    return match ? match[1] : idBobina
  }

  // Filtra i cavi in base alla ricerca
  const filteredCompatibili = caviCompatibili.filter(cavo =>
    !searchTerm || 
    cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cavo.tipologia?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredIncompatibili = caviIncompatibili.filter(cavo =>
    !searchTerm || 
    cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cavo.tipologia?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent 
        className="max-w-6xl w-[90vw] max-h-[90vh] overflow-y-auto"
        style={{ width: '90vw', maxWidth: '1200px' }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <Cable className="h-5 w-5" />
            TEST - Aggiungi cavi alla bobina {getBobinaNumber(bobina.id_bobina)}
          </DialogTitle>
          <DialogDescription>
            COMPONENTE DI TEST - Seleziona i cavi da associare alla bobina
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Info bobina */}
          <div className="bg-red-50 border border-red-200 p-3 rounded">
            <div className="text-red-800 font-medium mb-2">COMPONENTE DI TEST</div>
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div>Bobina: <strong>{getBobinaNumber(bobina.id_bobina)}</strong></div>
              <div>Tipo: <strong>{bobina.tipologia}</strong></div>
              <div>Formazione: <strong>{bobina.sezione}</strong></div>
              <div>Residui: <strong>{bobina.metri_residui}m</strong></div>
            </div>
            <div className="mt-2 text-sm">
              Selezionati: <strong>{caviSelezionati.length}</strong> cavi
            </div>
          </div>

          {/* Ricerca */}
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Cerca cavi..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>

          {/* Loading */}
          {caviLoading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Caricamento cavi...</span>
            </div>
          )}

          {/* Lista cavi compatibili */}
          {!caviLoading && (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">
                  Cavi Compatibili ({filteredCompatibili.length})
                </h3>
                <div className="space-y-2 max-h-60 overflow-y-auto border rounded p-2">
                  {filteredCompatibili.map(cavo => {
                    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)
                    const metri = caviMetri[cavo.id_cavo] || ''

                    return (
                      <div 
                        key={cavo.id_cavo}
                        className={`border rounded p-2 ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                      >
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => handleCavoToggle(cavo, true)}
                            className="h-4 w-4"
                          />
                          <span className="font-medium">{cavo.id_cavo}</span>
                          <span className="text-sm text-gray-600">
                            {cavo.tipologia} • {cavo.sezione} • {cavo.metri_teorici}m
                          </span>
                        </div>
                        
                        {isSelected && (
                          <div className="mt-2 flex items-center gap-2">
                            <label className="text-sm">Metri:</label>
                            <input
                              type="number"
                              value={metri}
                              onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                              className="w-20 px-2 py-1 border rounded text-sm"
                            />
                            <span className="text-sm">m</span>
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={saving}>
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving || caviSelezionati.length === 0}
          >
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {saving ? 'Salvataggio...' : `Salva ${caviSelezionati.length} cavi`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
